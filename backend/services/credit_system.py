"""
Credit system implementation for Atlas
Handles token-based pricing and credit consumption
"""

import logging
from typing import Dict, Optional, Any
from decimal import Decimal

logger = logging.getLogger(__name__)


def calculate_credits(usage_data: Dict[str, Any]) -> int:
    """
    Calculate credits based on actual usage
    
    Base formula:
    Credits = Base_Cost + Token_Cost + Tool_Cost + Duration_Cost
    """
    
    # Base cost per agent run
    base_cost = 5
    
    # Token-based cost (scaled to reasonable credit amounts)
    input_tokens = usage_data.get('input_tokens', 0)
    output_tokens = usage_data.get('output_tokens', 0)
    model = usage_data.get('model', 'gpt-4')
    
    # Credit rates per 1000 tokens (much higher than actual $ cost for simplicity)
    TOKEN_CREDIT_RATES = {
        'gpt-4': {'input': 2, 'output': 6},           # Premium model
        'gpt-3.5-turbo': {'input': 0.5, 'output': 1}, # Cheaper model
        'claude-3-sonnet': {'input': 1.5, 'output': 4.5},
        'anthropic/claude-3-5-haiku-latest': {'input': 1, 'output': 3},
        'anthropic/claude-3-7-sonnet-latest': {'input': 1.5, 'output': 4.5},
        'anthropic/claude-sonnet-4-********': {'input': 3, 'output': 9},
        'openrouter/deepseek/deepseek-chat': {'input': 0.3, 'output': 0.6},
        'openrouter/qwen/qwen3-235b-a22b': {'input': 2.5, 'output': 7.5},
        'openrouter/google/gemini-2.5-flash-preview-05-20': {'input': 1, 'output': 3},
        'default': {'input': 2, 'output': 6}
    }
    
    # Get model-specific rates or use default
    rates = TOKEN_CREDIT_RATES.get(model, TOKEN_CREDIT_RATES['default'])
    token_cost = (input_tokens * rates['input'] + output_tokens * rates['output']) / 1000
    
    # Tool usage cost
    tool_calls = usage_data.get('tool_calls', 0)
    tool_cost = tool_calls * 3  # 3 credits per tool call
    
    # Duration-based cost (for long-running tasks)
    duration_seconds = usage_data.get('duration_seconds', 0)
    duration_cost = max(0, (duration_seconds - 30) / 60) * 2  # 2 credits per minute after first 30 seconds
    
    total_credits = base_cost + token_cost + tool_cost + duration_cost
    return max(1, round(total_credits))  # Minimum 1 credit per run


def estimate_cost_from_credits(credits: int) -> Decimal:
    """
    Estimate dollar cost from credits
    Simplified: $0.01 per credit
    """
    return Decimal(str(credits * 0.01))


def get_model_credit_rates() -> Dict[str, Dict[str, float]]:
    """
    Get the credit rates for all supported models
    Returns the same rates used in calculate_credits
    """
    return {
        'gpt-4': {'input': 2, 'output': 6},
        'gpt-3.5-turbo': {'input': 0.5, 'output': 1},
        'claude-3-sonnet': {'input': 1.5, 'output': 4.5},
        'anthropic/claude-3-5-haiku-latest': {'input': 1, 'output': 3},
        'anthropic/claude-3-7-sonnet-latest': {'input': 1.5, 'output': 4.5},
        'anthropic/claude-sonnet-4-********': {'input': 3, 'output': 9},
        'openrouter/deepseek/deepseek-chat': {'input': 0.3, 'output': 0.6},
        'openrouter/qwen/qwen3-235b-a22b': {'input': 2.5, 'output': 7.5},
        'openrouter/google/gemini-2.5-flash-preview-05-20': {'input': 1, 'output': 3},
        'default': {'input': 2, 'output': 6}
    }


def validate_credit_balance(account_id: str, required_credits: int, supabase) -> Dict[str, Any]:
    """
    Check if account has sufficient credits for an operation
    """
    try:
        # Get account credit balance
        result = supabase.table('basejump.accounts').select('credit_balance, monthly_credit_limit, monthly_credit_used').eq('id', account_id).single().execute()
        
        if not result.data:
            return {
                'valid': False,
                'reason': 'Account not found',
                'credits_available': 0
            }
        
        account = result.data
        credits_available = account['credit_balance']
        
        if credits_available < required_credits:
            return {
                'valid': False,
                'reason': f'Insufficient credits. Required: {required_credits}, Available: {credits_available}',
                'credits_available': credits_available,
                'required_credits': required_credits
            }
        
        return {
            'valid': True,
            'credits_available': credits_available,
            'required_credits': required_credits
        }
        
    except Exception as e:
        logger.error(f"Error validating credit balance for account {account_id}: {e}")
        return {
            'valid': False,
            'reason': 'Error checking credit balance',
            'credits_available': 0
        }


def record_credit_usage(account_id: str, thread_id: str, agent_run_id: str, 
                       message_id: str, usage_data: Dict[str, Any], supabase) -> Dict[str, Any]:
    """
    Record token usage and consume credits
    """
    try:
        # Calculate credits
        credits_consumed = calculate_credits(usage_data)
        
        # Call the database function to record usage and consume credits
        result = supabase.rpc('record_token_usage_and_consume_credits', {
            'p_account_id': account_id,
            'p_thread_id': thread_id,
            'p_agent_run_id': agent_run_id,
            'p_message_id': message_id,
            'p_model': usage_data.get('model', 'unknown'),
            'p_input_tokens': usage_data.get('input_tokens', 0),
            'p_output_tokens': usage_data.get('output_tokens', 0),
            'p_tool_calls': usage_data.get('tool_calls', 0),
            'p_duration_seconds': usage_data.get('duration_seconds', 0)
        }).execute()
        
        if result.data:
            logger.info(f"Recorded credit usage for account {account_id}: {credits_consumed} credits")
            return result.data
        else:
            logger.error(f"Failed to record credit usage for account {account_id}")
            return {'error': 'Failed to record usage'}
            
    except Exception as e:
        logger.error(f"Error recording credit usage for account {account_id}: {e}")
        return {'error': str(e)}


def add_credits_to_account(account_id: str, credits_amount: int, 
                          description: str, supabase) -> Dict[str, Any]:
    """
    Add credits to an account (for purchases or refunds)
    """
    try:
        result = supabase.rpc('add_credits', {
            'p_account_id': account_id,
            'p_credits_amount': credits_amount,
            'p_description': description
        }).execute()
        
        if result.data:
            logger.info(f"Added {credits_amount} credits to account {account_id}")
            return result.data
        else:
            logger.error(f"Failed to add credits to account {account_id}")
            return {'error': 'Failed to add credits'}
            
    except Exception as e:
        logger.error(f"Error adding credits to account {account_id}: {e}")
        return {'error': str(e)}


def get_credit_balance(account_id: str, supabase) -> Dict[str, Any]:
    """
    Get current credit balance and usage stats for an account
    """
    try:
        result = supabase.table('basejump.accounts').select(
            'credit_balance, monthly_credit_limit, monthly_credit_used, credit_reset_date'
        ).eq('id', account_id).single().execute()
        
        if result.data:
            return {
                'success': True,
                'credit_balance': result.data['credit_balance'],
                'monthly_credit_limit': result.data['monthly_credit_limit'],
                'monthly_credit_used': result.data['monthly_credit_used'],
                'credit_reset_date': result.data['credit_reset_date']
            }
        else:
            return {'success': False, 'error': 'Account not found'}
            
    except Exception as e:
        logger.error(f"Error getting credit balance for account {account_id}: {e}")
        return {'success': False, 'error': str(e)}


# Credit tier configuration (converted from message limits)
SUBSCRIPTION_CREDIT_LIMITS = {
    "free": 100,        # 10 messages -> 100 credits  
    "plus": 750,        # 75 messages -> 750 credits
    "pro_75": 1500,     # 150 messages -> 1500 credits
    "admin": 100000,    # Unlimited -> 100k credits
}


def get_credit_limit_for_subscription(subscription_tier: str) -> int:
    """
    Get credit limit for a subscription tier
    """
    return SUBSCRIPTION_CREDIT_LIMITS.get(subscription_tier, 100)  # Default to free tier