"""
Credit Calculation Engine for Atlas

This module calculates credit costs based on actual usage including:
- Token consumption (input/output)
- Model pricing tiers
- Tool usage
- Task duration
- Complexity factors

The goal is to provide fair, usage-based pricing while maintaining
simple credit amounts that users can understand.
"""

import math
from typing import Dict, Any, Optional
from utils.logger import logger

# Credit rates per 1000 tokens for different models
# These rates are designed to be user-friendly (whole numbers) while
# reflecting the relative cost differences between models
TOKEN_CREDIT_RATES = {
    # OpenAI Models
    "gpt-4": {"input": 3, "output": 9},                    # Premium model
    "gpt-4-turbo": {"input": 2, "output": 6},              # Balanced premium
    "gpt-3.5-turbo": {"input": 1, "output": 2},            # Budget model
    "gpt-4o": {"input": 2, "output": 6},                   # Latest model
    "gpt-4o-mini": {"input": 1, "output": 2},              # Mini model
    
    # Anthropic Models  
    "claude-3-opus": {"input": 4, "output": 12},           # Most expensive
    "claude-3-sonnet": {"input": 2, "output": 6},          # Balanced
    "claude-3-haiku": {"input": 1, "output": 2},           # Fastest/cheapest
    "claude-3-5-sonnet": {"input": 2, "output": 6},        # Latest sonnet
    
    # OpenRouter Models
    "openrouter/deepseek/deepseek-chat": {"input": 1, "output": 2},
    "openrouter/qwen/qwen3-235b-a22b": {"input": 1, "output": 2},
    
    # Google Models
    "google/gemini-2.5-flash-preview-05-20": {"input": 1, "output": 2},
    "google/gemini-pro": {"input": 1, "output": 2},
    
    # Default fallback for unknown models
    "default": {"input": 2, "output": 6}
}

# Tool usage costs (credits per tool call)
TOOL_CREDIT_COSTS = {
    "web_search": 5,           # Web browsing/search
    "code_execution": 8,       # Code running in sandbox
    "file_upload": 3,          # File processing
    "image_generation": 15,    # AI image generation
    "data_analysis": 10,       # Complex data processing
    "api_call": 4,             # External API calls
    "default": 3               # Default tool cost
}

# Base costs
BASE_CREDIT_COST = 2           # Minimum cost per agent run
DURATION_CREDIT_RATE = 1       # Credits per minute after first 30 seconds
FREE_DURATION_SECONDS = 30     # First 30 seconds are free

class CreditCalculator:
    """Calculates credit costs for agent runs based on actual usage."""
    
    @staticmethod
    def calculate_token_credits(input_tokens: int, output_tokens: int, model: str) -> float:
        """Calculate credits based on token usage and model type."""
        try:
            # Normalize model name for lookup
            model_key = model.lower().strip()
            
            # Find matching rate
            rates = None
            for rate_key, rate_value in TOKEN_CREDIT_RATES.items():
                if rate_key in model_key or model_key in rate_key:
                    rates = rate_value
                    break
            
            # Use default if no match found
            if not rates:
                rates = TOKEN_CREDIT_RATES["default"]
                logger.warning(f"Using default token rates for unknown model: {model}")
            
            # Calculate credits (rates are per 1000 tokens)
            input_credits = (input_tokens * rates["input"]) / 1000
            output_credits = (output_tokens * rates["output"]) / 1000
            
            total_credits = input_credits + output_credits
            
            logger.debug(f"Token credits for {model}: {input_tokens}in + {output_tokens}out = {total_credits:.2f} credits")
            
            return total_credits
            
        except Exception as e:
            logger.error(f"Error calculating token credits: {str(e)}")
            return 0.0
    
    @staticmethod
    def calculate_tool_credits(tool_calls: int, tool_types: Optional[list] = None) -> float:
        """Calculate credits based on tool usage."""
        try:
            if tool_calls == 0:
                return 0.0
            
            if not tool_types:
                # Use default cost if no specific tool types provided
                return tool_calls * TOOL_CREDIT_COSTS["default"]
            
            total_credits = 0.0
            for tool_type in tool_types:
                tool_cost = TOOL_CREDIT_COSTS.get(tool_type, TOOL_CREDIT_COSTS["default"])
                total_credits += tool_cost
            
            logger.debug(f"Tool credits: {tool_calls} calls = {total_credits} credits")
            
            return total_credits
            
        except Exception as e:
            logger.error(f"Error calculating tool credits: {str(e)}")
            return 0.0
    
    @staticmethod
    def calculate_duration_credits(duration_seconds: int) -> float:
        """Calculate credits based on task duration."""
        try:
            if duration_seconds <= FREE_DURATION_SECONDS:
                return 0.0
            
            # Charge for time beyond free duration
            billable_seconds = duration_seconds - FREE_DURATION_SECONDS
            billable_minutes = billable_seconds / 60
            
            duration_credits = billable_minutes * DURATION_CREDIT_RATE
            
            logger.debug(f"Duration credits: {duration_seconds}s = {duration_credits:.2f} credits")
            
            return duration_credits
            
        except Exception as e:
            logger.error(f"Error calculating duration credits: {str(e)}")
            return 0.0
    
    @staticmethod
    def calculate_total_credits(usage_data: Dict[str, Any]) -> int:
        """
        Calculate total credits for an agent run.
        
        Args:
            usage_data: Dictionary containing:
                - input_tokens: Number of input tokens
                - output_tokens: Number of output tokens  
                - model: Model name used
                - tool_calls: Number of tool calls
                - tool_types: List of tool types used (optional)
                - duration_seconds: Task duration in seconds
                
        Returns:
            Total credits as integer (rounded up)
        """
        try:
            # Extract usage data with defaults
            input_tokens = usage_data.get("input_tokens", 0)
            output_tokens = usage_data.get("output_tokens", 0)
            model = usage_data.get("model", "gpt-4")
            tool_calls = usage_data.get("tool_calls", 0)
            tool_types = usage_data.get("tool_types", [])
            duration_seconds = usage_data.get("duration_seconds", 0)
            
            # Calculate component costs
            token_credits = CreditCalculator.calculate_token_credits(input_tokens, output_tokens, model)
            tool_credits = CreditCalculator.calculate_tool_credits(tool_calls, tool_types)
            duration_credits = CreditCalculator.calculate_duration_credits(duration_seconds)
            
            # Total credits (including base cost)
            total_credits = BASE_CREDIT_COST + token_credits + tool_credits + duration_credits
            
            # Round up to nearest integer (minimum 1 credit)
            final_credits = max(1, math.ceil(total_credits))
            
            logger.info(f"Credit calculation: base={BASE_CREDIT_COST} + tokens={token_credits:.2f} + tools={tool_credits:.2f} + duration={duration_credits:.2f} = {final_credits} credits")
            
            return final_credits
            
        except Exception as e:
            logger.error(f"Error calculating total credits: {str(e)}")
            return 1  # Minimum fallback cost
    
    @staticmethod
    def estimate_credits(input_text: str, model: str = "gpt-4", estimated_output_ratio: float = 0.5) -> int:
        """
        Estimate credits needed for a task before execution.
        
        Args:
            input_text: The input text/prompt
            model: Model to be used
            estimated_output_ratio: Estimated output tokens as ratio of input tokens
            
        Returns:
            Estimated credits needed
        """
        try:
            # Rough token estimation (4 characters per token average)
            estimated_input_tokens = len(input_text) // 4
            estimated_output_tokens = int(estimated_input_tokens * estimated_output_ratio)
            
            # Create usage data for estimation
            usage_data = {
                "input_tokens": estimated_input_tokens,
                "output_tokens": estimated_output_tokens,
                "model": model,
                "tool_calls": 1,  # Assume at least one tool call
                "duration_seconds": 60  # Assume 1 minute duration
            }
            
            estimated_credits = CreditCalculator.calculate_total_credits(usage_data)
            
            logger.debug(f"Estimated credits for input ({len(input_text)} chars): {estimated_credits}")
            
            return estimated_credits
            
        except Exception as e:
            logger.error(f"Error estimating credits: {str(e)}")
            return 10  # Conservative fallback estimate
    
    @staticmethod
    def get_credit_breakdown(usage_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed breakdown of credit calculation.
        
        Returns:
            Dictionary with detailed cost breakdown
        """
        try:
            input_tokens = usage_data.get("input_tokens", 0)
            output_tokens = usage_data.get("output_tokens", 0)
            model = usage_data.get("model", "gpt-4")
            tool_calls = usage_data.get("tool_calls", 0)
            tool_types = usage_data.get("tool_types", [])
            duration_seconds = usage_data.get("duration_seconds", 0)
            
            token_credits = CreditCalculator.calculate_token_credits(input_tokens, output_tokens, model)
            tool_credits = CreditCalculator.calculate_tool_credits(tool_calls, tool_types)
            duration_credits = CreditCalculator.calculate_duration_credits(duration_seconds)
            total_credits = CreditCalculator.calculate_total_credits(usage_data)
            
            return {
                "base_cost": BASE_CREDIT_COST,
                "token_cost": round(token_credits, 2),
                "tool_cost": round(tool_credits, 2),
                "duration_cost": round(duration_credits, 2),
                "total_credits": total_credits,
                "breakdown": {
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                    "model": model,
                    "tool_calls": tool_calls,
                    "duration_seconds": duration_seconds
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting credit breakdown: {str(e)}")
            return {"total_credits": 1, "error": str(e)}
