'use client';

import { useUsageIndicator } from '@/hooks/use-usage-indicator';

export interface RunValidationResult {
  canSubmit: boolean;
  reason?: string;
  creditsRemaining?: number;
  runsRemaining?: number; // Legacy field
  isFreePlan?: boolean;
}

export const useRunValidation = (): RunValidationResult => {
  const usageData = useUsageIndicator();

  // If loading, be conservative and block
  if (usageData.isLoading) {
    return {
      canSubmit: false,
      reason: 'Loading subscription data...',
      creditsRemaining: 0,
      runsRemaining: 0, // Legacy field
      isFreePlan: true,
    };
  }

  // Check if user has sufficient credits (new credit system)
  if (!usageData.hasCreditsLeft) {
    return {
      canSubmit: false,
      reason: usageData.isFreeUser
        ? `Insufficient credits. You need at least 5 credits to run an agent. Current balance: ${usageData.creditBalance} credits. Upgrade to continue using Atlas.`
        : `Insufficient credits. You need at least 5 credits to run an agent. Current balance: ${usageData.creditBalance} credits.`,
      creditsRemaining: usageData.creditBalance,
      runsRemaining: usageData.messagesLeft, // Legacy field
      isFreePlan: usageData.isFreeUser,
    };
  }

  return {
    canSubmit: true,
    creditsRemaining: usageData.creditBalance,
    runsRemaining: usageData.messagesLeft, // Legacy field
    isFreePlan: usageData.isFreeUser,
  };
};
