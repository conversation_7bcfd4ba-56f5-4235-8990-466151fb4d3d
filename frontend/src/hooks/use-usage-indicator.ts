'use client';

import { useSubscription } from '@/hooks/react-query/subscriptions/use-subscriptions';
import { useQueryClient } from '@tanstack/react-query';
import { subscriptionKeys } from '@/hooks/react-query/subscriptions/keys';
import { isLocalMode } from '@/lib/config';
import { useEffect } from 'react';

export interface UsageIndicatorData {
  // Credit system fields
  creditsLeft: number;
  creditsLimit: number;
  creditsUsed: number;
  hasCreditsLeft: boolean;
  creditBalance: number;
  // Legacy message fields (for backward compatibility)
  messagesLeft: number;
  messagesLimit: number;
  currentUsage: number;
  hasMessagesLeft: boolean;
  isFreeUser: boolean;
  isLoading: boolean;
  shouldShowUpgrade: boolean;
}

/**
 * Unified hook for usage indicator data across all pages
 * Ensures consistent data and automatic cache invalidation
 */
export const useUsageIndicator = (): UsageIndicatorData => {
  const { data: subscriptionData, isLoading } = useSubscription();
  const queryClient = useQueryClient();

  // Invalidate cache when component mounts to ensure fresh data
  useEffect(() => {
    if (!isLocalMode()) {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.details() });
    }
  }, [queryClient]);

  // In local mode, return mock data
  if (isLocalMode()) {
    return {
      // Credit fields
      creditsLeft: 9999,
      creditsLimit: 9999,
      creditsUsed: 0,
      hasCreditsLeft: true,
      creditBalance: 9999,
      // Legacy message fields
      messagesLeft: 999,
      messagesLimit: 999,
      currentUsage: 0,
      hasMessagesLeft: true,
      isFreeUser: false,
      isLoading: false,
      shouldShowUpgrade: false,
    };
  }

  // If no subscription data yet, return loading state
  if (!subscriptionData || isLoading) {
    return {
      // Credit fields
      creditsLeft: 0,
      creditsLimit: 0,
      creditsUsed: 0,
      hasCreditsLeft: false,
      creditBalance: 0,
      // Legacy message fields
      messagesLeft: 0,
      messagesLimit: 0,
      currentUsage: 0,
      hasMessagesLeft: false,
      isFreeUser: true,
      isLoading: true,
      shouldShowUpgrade: false,
    };
  }

  // Extract credit data from subscription (new credit system)
  const creditsLimit = subscriptionData.credits_limit || subscriptionData.messages_limit * 10 || 100;
  const creditBalance = subscriptionData.credit_balance || 0;
  const creditsUsed = subscriptionData.monthly_credit_used || 0;
  const creditsLeft = creditBalance;
  const hasCreditsLeft = creditBalance > 5; // Minimum 5 credits needed for a run

  // Legacy message data (for backward compatibility)
  const messagesLimit = subscriptionData.messages_limit || 0;
  const currentUsage = subscriptionData.current_usage || 0;
  const messagesLeft = Math.max(0, messagesLimit - currentUsage);
  const hasMessagesLeft = messagesLeft > 0;
  
  const isFreeUser = subscriptionData.plan_name === 'free';
  const shouldShowUpgrade = !hasCreditsLeft && isFreeUser;

  return {
    // Credit system data
    creditsLeft,
    creditsLimit,
    creditsUsed,
    hasCreditsLeft,
    creditBalance,
    // Legacy message data
    messagesLeft,
    messagesLimit,
    currentUsage,
    hasMessagesLeft,
    isFreeUser,
    isLoading: false,
    shouldShowUpgrade,
  };
};

/**
 * Hook to manually refresh usage data
 * Useful after operations that might change usage
 */
export const useRefreshUsage = () => {
  const queryClient = useQueryClient();

  return () => {
    if (!isLocalMode()) {
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.details() });
      queryClient.invalidateQueries({ queryKey: ['billing', 'status'] });
    }
  };
};
